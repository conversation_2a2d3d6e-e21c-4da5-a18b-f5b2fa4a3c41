// @ts-nocheck
export {};

; declare global {
	const __VLS_directiveBindingRestFields: { instance: null, oldValue: null, modifiers: any, dir: any };
	const __VLS_unref: typeof import('@mpxjs/core').unref;
	const __VLS_placeholder: any;

	type __VLS_NativeElements = __VLS_SpreadMerge<SVGElementTagNameMap, HTMLElementTagNameMap>;
	type __VLS_NativeComponents = import('./mpx_native_components').MpxNativeComponents;
	type __VLS_Element = import('./mpx_native_components').MpxElement;
	type __VLS_GlobalComponents = import('@mpxjs/core').GlobalComponents;
	type __VLS_GlobalDirectives = import('@mpxjs/core').GlobalDirectives;
	type __VLS_IsAny<T> = 0 extends 1 & T ? true : false;
	type __VLS_PickNotAny<A, B> = __VLS_IsAny<A> extends true ? B : A;
	type __VLS_SpreadMerge<A, B> = Omit<A, keyof B> & B;
	type __VLS_WithComponent<N0 extends string, LocalComponents, Self, N1 extends string, N2 extends string, N3 extends string> =
		N1 extends keyof LocalComponents ? N1 extends N0 ? Pick<LocalComponents, N0 extends keyof LocalComponents ? N0 : never> : { [K in N0]: LocalComponents[N1] } :
		N2 extends keyof LocalComponents ? N2 extends N0 ? Pick<LocalComponents, N0 extends keyof LocalComponents ? N0 : never> : { [K in N0]: LocalComponents[N2] } :
		N3 extends keyof LocalComponents ? N3 extends N0 ? Pick<LocalComponents, N0 extends keyof LocalComponents ? N0 : never> : { [K in N0]: LocalComponents[N3] } :
		Self extends object ? { [K in N0]: Self } :
		N1 extends keyof __VLS_GlobalComponents ? N1 extends N0 ? Pick<__VLS_GlobalComponents, N0 extends keyof __VLS_GlobalComponents ? N0 : never> : { [K in N0]: __VLS_GlobalComponents[N1] } :
		N2 extends keyof __VLS_GlobalComponents ? N2 extends N0 ? Pick<__VLS_GlobalComponents, N0 extends keyof __VLS_GlobalComponents ? N0 : never> : { [K in N0]: __VLS_GlobalComponents[N2] } :
		N3 extends keyof __VLS_GlobalComponents ? N3 extends N0 ? Pick<__VLS_GlobalComponents, N0 extends keyof __VLS_GlobalComponents ? N0 : never> : { [K in N0]: __VLS_GlobalComponents[N3] } :
		{ [K in N0]: unknown };
	type __VLS_FunctionalComponentCtx<T, K> = __VLS_PickNotAny<'__ctx' extends keyof __VLS_PickNotAny<K, {}>
		? K extends { __ctx?: infer Ctx } ? NonNullable<Ctx> : never : any
		, T extends (props: any, ctx: infer Ctx) => any ? Ctx : any
	>;
	type __VLS_FunctionalComponentProps<T, K> = '__ctx' extends keyof __VLS_PickNotAny<K, {}>
		? K extends { __ctx?: { props?: infer P } } ? NonNullable<P> : never
		: T extends (props: infer P, ...args: any) => any ? P
		: {};
	type __VLS_FunctionalComponent<T> = (props: (T extends { $props: infer Props } ? Props : {}) & Record<string, unknown>, ctx?: any) => __VLS_Element & {
		__ctx?: {
			attrs?: any,
			slots?: T extends { $slots: infer Slots } ? Slots : Record<string, any>,
			emit?: T extends { $emit: infer Emit } ? Emit : {},
			props?: (T extends { $props: infer Props } ? Props : {}) & Record<string, unknown>,
			expose?: (exposed: T) => void,
		}
	};
	type __VLS_NormalizeSlotReturns<S, R = NonNullable<S> extends (...args: any) => infer K ? K : any> = R extends any[] ? {
		[K in keyof R]: R[K] extends infer V
			? V extends Element ? V
			: V extends new (...args: any) => infer R ? ReturnType<__VLS_FunctionalComponent<R>>
			: V extends (...args: any) => infer R ? R
			: any
			: never
	} : R;
	type __VLS_IsFunction<T, K> = K extends keyof T
		? __VLS_IsAny<T[K]> extends false
		? unknown extends T[K]
		? false
		: true
		: false
		: false;
	type __VLS_NormalizeComponentEvent<Props, Emits, onEvent extends keyof Props, Event extends keyof Emits, CamelizedEvent extends keyof Emits> = (
		__VLS_IsFunction<Props, onEvent> extends true
			? Props
			: __VLS_IsFunction<Emits, Event> extends true
				? { [K in onEvent]?: Emits[Event] }
				: __VLS_IsFunction<Emits, CamelizedEvent> extends true
					? { [K in onEvent]?: Emits[CamelizedEvent] }
					: Props
	) & Record<string, unknown>;
	type __VLS_UnionToIntersection<U> = (U extends unknown ? (arg: U) => unknown : never) extends ((arg: infer P) => unknown) ? P : never;
	type __VLS_OverloadUnionInner<T, U = unknown> = U & T extends (...args: infer A) => infer R
		? U extends T
		? never
		: __VLS_OverloadUnionInner<T, Pick<T, keyof T> & U & ((...args: A) => R)> | ((...args: A) => R)
		: never;
	type __VLS_OverloadUnion<T> = Exclude<
		__VLS_OverloadUnionInner<(() => never) & T>,
		T extends () => never ? never : () => never
	>;
	type __VLS_ConstructorOverloads<T> = __VLS_OverloadUnion<T> extends infer F
		? F extends (event: infer E, ...args: infer A) => any
		? { [K in E & string]: (...args: A) => void; }
		: never
		: never;
	type __VLS_NormalizeEmits<T> = __VLS_PrettifyGlobal<
		__VLS_UnionToIntersection<
			__VLS_ConstructorOverloads<T> & {
				[K in keyof T]: T[K] extends any[] ? { (...args: T[K]): void } : never
			}
		>
	>;
	type __VLS_ResolveEmits<
		Comp,
		Emits,
		TypeEmits = Comp extends { __typeEmits?: infer T } ? unknown extends T ? {} : import('@mpxjs/core').ShortEmitsToObject<T> : {},
		NormalizedEmits = __VLS_NormalizeEmits<Emits> extends infer E ? string extends keyof E ? {} : E : never,
	> = __VLS_SpreadMerge<NormalizedEmits, TypeEmits>;
	type __VLS_ResolveDirectives<T> = {
		[K in Exclude<keyof T, keyof __VLS_GlobalDirectives> & string as `v${Capitalize<K>}`]: T[K];
	};
	type __VLS_PrettifyGlobal<T> = { [K in keyof T]: T[K]; } & {};
	type __VLS_UseTemplateRef<T> = Readonly<import('@mpxjs/core').ShallowRef<T | null>>;

	function __VLS_getWxForSourceType<T extends number | string | any[] | Iterable<any>>(source: T): [
		item: T extends number ? number
			: T extends string ? string
			: T extends any[] ? T[number]
			: T extends Iterable<infer T1> ? T1
			: any,
		index: number,
	][];
	function __VLS_getWxForSourceType<T>(source: T): [
		item: T[keyof T],
		key: keyof T,
		index: number,
	][];
	function __VLS_getSlotParameters<S, D extends S>(slot: S, decl?: D):
		__VLS_PickNotAny<NonNullable<D>, (...args: any) => any> extends (...args: infer P) => any ? P : any[];
	function __VLS_asFunctionalDirective<T>(dir: T): T extends import('@mpxjs/core').ObjectDirective
		? NonNullable<T['created' | 'beforeMount' | 'mounted' | 'beforeUpdate' | 'updated' | 'beforeUnmount' | 'unmounted']>
		: T extends (...args: any) => any
			? T
			: (arg1: unknown, arg2: unknown, arg3: unknown, arg4: unknown) => void;
	function __VLS_makeOptional<T>(t: T): { [K in keyof T]?: T[K] };
	function __VLS_asFunctionalComponent<T, K = T extends new (...args: any) => any ? InstanceType<T> : unknown>(t: T, instance?: K):
		T extends new (...args: any) => any ? __VLS_FunctionalComponent<K>
		: T extends () => any ? (props: {}, ctx?: any) => ReturnType<T>
		
		: T extends (...args: any) => any ? T
		: __VLS_FunctionalComponent<{}>;
	function __VLS_functionalComponentArgsRest<T extends (...args: any) => any>(t: T): 2 extends Parameters<T>['length'] ? [any] : [];
	function __VLS_asFunctionalElement<T>(tag: T, endTag?: T): (attrs: T & Record<string, unknown>) => void;
	function __VLS_asFunctionalSlot<S>(slot: S): S extends () => infer R ? (props: {}) => R : NonNullable<S>;
	function __VLS_tryAsConstant<const T>(t: T): T;

	type I18nValues = { [k: string]: string } | Array<string>

	type UnwrapRefs<T> = {
		[K in keyof T]: T[K] extends import('@mpxjs/core').Ref
			? import('@mpxjs/core').UnwrapRef<T[K]>
			: T[K]
	}
	
  // #region DefineComponent - global types
  function DefineComponent<
    D extends Data = {},
    P extends Properties = {},
    C = {},
    M extends Methods = {},
    Mi extends Array<any> = [],
    S extends AnyObject = {},
    O extends AnyObject = {},
  >(
    opt: ThisTypedComponentOpt<D, P, C, M, Mi, S, O>,
    config?: { customCtor: any }
  ): ComponentIns<D, P, C, M, Mi, S, O> & MpxExtractReactHooksExecReturnType<O>
  // #endregion

  // #region DefinePage - global types
  function DefinePage<
    D extends Data = {},
    P extends Properties = {},
    C = {},
    M extends Methods = {},
    Mi extends Array<any> = [],
    O extends AnyObject = {},
  >(
    opt: ThisTypedPageOpt<D, P, C, M, Mi, O>,
    config?: { customCtor: any }
  ): ComponentIns<D, P, C, M, Mi, O>
  // #endregion

}

// #region DefineComponent - local types
type Data = object | (() => object)
interface Properties {
  [key: string]: WechatMiniprogram.Component.AllProperty
}
interface Methods {
  [key: string]: (...args: any[]) => any
}
type ObjectOf<T> = {
  [key: string]: T
}
type AnyObject = ObjectOf<any>
type ThisTypedComponentOpt<
  D extends Data,
  P extends Properties,
  C,
  M extends Methods,
  Mi extends Array<any>,
  S extends Record<any, any>,
  O = {},
> = ComponentOpt<D, P, C, M, Mi, S> & ThisType<ComponentIns<D, P, C, M, Mi, S, O>> & O
interface ComponentOpt<
  D extends Data,
  P extends Properties,
  C,
  M extends Methods,
  Mi extends Array<any>,
  S extends Record<any, any>,
> extends Partial<WechatMiniprogram.Component.Lifetimes & WechatMiniprogram.Component.OtherOption> {
  data?: D
  properties?: P
  computed?: C
  methods?: M
  mixins?: Mi
  watch?: WatchField
  setup?: (props: GetPropsType<P & UnboxMixinsField<Mi, 'properties'>>, context: any) => S
  pageShow?: () => void
  pageHide?: () => void
  initData?: Record<string, any>
  provide?: Record<string, any> | (() => Record<string, any>)
  inject?:
    | {
        [key: string]: string | Symbol | { from?: string | Symbol; default?: any }
      }
    | Array<string>
  [index: string]: any
}
type PageOpt<
  D extends Data,
  P extends Properties,
  C,
  M extends Methods,
  Mi extends Array<any>,
  S extends Record<any, any>,
> = ComponentOpt<D, P, C, M, Mi, S> & Partial<WechatMiniprogram.Page.ILifetime>
type ThisTypedPageOpt<
  D extends Data,
  P extends Properties,
  C,
  M extends Methods,
  Mi extends Array<any>,
  S extends Record<any, any>,
  O = {},
> = PageOpt<D, P, C, M, Mi, S> & ThisType<ComponentIns<D, P, C, M, Mi, S, O>> & O
interface WatchOpt {
  immediate?: boolean
  immediateAsync?: boolean
  deep?: boolean
  sync?: boolean
  once?: boolean | ((newVal: any, oldVal: any) => boolean)
}
interface WatchOptWithHandler extends WatchOpt {
  handler?: WatchHandler
}
interface WatchHandler {
  (val: any, oldVal?: any): void
}
interface WatchField {
  [key: string]: WatchHandler | WatchOptWithHandler
}
type GetPropsType<T extends Properties> = {
  readonly [K in keyof T]: T[K] extends FullPropType<infer V>
    ? V
    : T[K] extends import('@mpxjs/core').PropType<infer V>
      ? V
      : WechatMiniprogram.Component.PropertyToData<T[K]>
}
type FullPropType<T> = {
  type: import('@mpxjs/core').PropType<T>
  value?: T
  optionalTypes?: WechatMiniprogram.Component.ShortProperty[]
}
type UnboxMixinField<T extends Mixin<{}, {}, {}, {}>, F> = F extends keyof T ? T[F] : {}
type UnboxMixinsField<Mi extends Array<any>, F> = UnionToIntersection<
  RequiredPropertiesForUnion<UnboxMixinField<ArrayType<Mi>, F>>
>
type UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends (k: infer I) => void ? I : never
type RequiredPropertiesForUnion<T> = T extends object ? Pick<T, RequiredPropertyNames<T>> : never
type RequiredPropertyNames<T> = {
  [K in keyof T]-?: T[K] extends undefined ? never : K
}[keyof T]
type ArrayType<T extends any[]> = T extends Array<infer R> ? R : never
interface Mixin<D, P, C, M> {
  data?: D
  properties?: P
  computed?: C
  methods?: M
  [index: string]: any
}
type ComponentIns<
  D extends Data = {},
  P extends Properties = {},
  C = {},
  M extends Methods = {},
  Mi extends Array<any> = [],
  S extends Record<any, any> = {},
  O = {},
> = GetDataType<D> &
  UnboxMixinsField<Mi, 'data'> &
  M &
  UnboxMixinsField<Mi, 'methods'> & {
    [K in keyof S]: S[K] extends import('@mpxjs/core').Ref<infer V> ? V : S[K]
  } & GetPropsType<P & UnboxMixinsField<Mi, 'properties'>> &
  GetComputedType<C & UnboxMixinsField<Mi, 'computed'>> &
  WxComponentIns<D, P, M> &
  MpxComponentIns &
  MpxComProps<O>
type GetDataType<T> = T extends () => any ? ReturnType<T> : T
type MpxComProps<O> = { $rawOptions: O }
type MpxComponentIns = import('@mpxjs/core').MpxComponentIns
type GetComputedType<T> = {
  [K in keyof T]: T[K] extends { get: (...args: any[]) => infer R }
    ? R
    : T[K] extends (...args: any[]) => infer R
      ? R
      : never
}
type WxComponentIns<D extends Data = {}, P extends Properties = {}, M extends Methods = {}> = Omit<
  WechatMiniprogram.Component.Instance<D, P, M, any>,
  'selectComponent' | 'selectAllComponents'
> &
  ReplaceWxComponentIns
interface ReplaceWxComponentIns {
  selectComponent(selector: string): ComponentIns<{}, {}, {}, {}, []>
  selectAllComponents(selector: string): Array<ComponentIns<{}, {}, {}, {}, []>>
}
type MpxExtractReactHooksExecReturnType<O> = O['__REACTHOOKSEXEC'] extends (...args: any[]) => any
	? ReturnType<O['__REACTHOOKSEXEC']>
	: {}
// #endregion
