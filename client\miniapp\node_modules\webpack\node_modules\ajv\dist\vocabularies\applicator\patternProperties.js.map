{"version": 3, "file": "patternProperties.js", "sourceRoot": "", "sources": ["../../../lib/vocabularies/applicator/patternProperties.ts"], "names": [], "mappings": ";;AAEA,kCAAuD;AACvD,mDAAkD;AAClD,6CAAqE;AACrE,6CAA6D;AAG7D,MAAM,GAAG,GAA0B;IACjC,OAAO,EAAE,mBAAmB;IAC5B,IAAI,EAAE,QAAQ;IACd,UAAU,EAAE,QAAQ;IACpB,IAAI,CAAC,GAAe;QAClB,MAAM,EAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE,EAAC,GAAG,GAAG,CAAA;QACjD,MAAM,EAAC,IAAI,EAAC,GAAG,EAAE,CAAA;QACjB,MAAM,QAAQ,GAAG,IAAA,0BAAmB,EAAC,MAAM,CAAC,CAAA;QAC5C,MAAM,mBAAmB,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAChD,IAAA,wBAAiB,EAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAc,CAAC,CAC9C,CAAA;QAED,IACE,QAAQ,CAAC,MAAM,KAAK,CAAC;YACrB,CAAC,mBAAmB,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM;gBAC7C,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,EAC9C,CAAC;YACD,OAAM;QACR,CAAC;QAED,MAAM,eAAe,GACnB,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI,YAAY,CAAC,UAAU,CAAA;QAC/E,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAC/B,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,KAAK,YAAY,cAAI,CAAC,EAAE,CAAC;YACrD,EAAE,CAAC,KAAK,GAAG,IAAA,2BAAoB,EAAC,GAAG,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;QAChD,CAAC;QACD,MAAM,EAAC,KAAK,EAAC,GAAG,EAAE,CAAA;QAClB,yBAAyB,EAAE,CAAA;QAE3B,SAAS,yBAAyB;YAChC,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;gBAC3B,IAAI,eAAe;oBAAE,uBAAuB,CAAC,GAAG,CAAC,CAAA;gBACjD,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC;oBACjB,kBAAkB,CAAC,GAAG,CAAC,CAAA;gBACzB,CAAC;qBAAM,CAAC;oBACN,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA,CAAC,WAAW;oBAChC,kBAAkB,CAAC,GAAG,CAAC,CAAA;oBACvB,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;gBACf,CAAC;YACH,CAAC;QACH,CAAC;QAED,SAAS,uBAAuB,CAAC,GAAW;YAC1C,KAAK,MAAM,IAAI,IAAI,eAAe,EAAE,CAAC;gBACnC,IAAI,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC/B,IAAA,sBAAe,EACb,EAAE,EACF,YAAY,IAAI,oBAAoB,GAAG,gCAAgC,CACxE,CAAA;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,SAAS,kBAAkB,CAAC,GAAW;YACrC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,GAAG,EAAE,EAAE;gBAC7B,GAAG,CAAC,EAAE,CAAC,IAAA,WAAC,EAAA,GAAG,IAAA,iBAAU,EAAC,GAAG,EAAE,GAAG,CAAC,SAAS,GAAG,GAAG,EAAE,GAAG,EAAE;oBACnD,MAAM,WAAW,GAAG,mBAAmB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;oBACrD,IAAI,CAAC,WAAW,EAAE,CAAC;wBACjB,GAAG,CAAC,SAAS,CACX;4BACE,OAAO,EAAE,mBAAmB;4BAC5B,UAAU,EAAE,GAAG;4BACf,QAAQ,EAAE,GAAG;4BACb,YAAY,EAAE,WAAI,CAAC,GAAG;yBACvB,EACD,KAAK,CACN,CAAA;oBACH,CAAC;oBAED,IAAI,EAAE,CAAC,IAAI,CAAC,WAAW,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;wBAC1C,GAAG,CAAC,MAAM,CAAC,IAAA,WAAC,EAAA,GAAG,KAAK,IAAI,GAAG,GAAG,EAAE,IAAI,CAAC,CAAA;oBACvC,CAAC;yBAAM,IAAI,CAAC,WAAW,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC;wBACzC,sFAAsF;wBACtF,uDAAuD;wBACvD,GAAG,CAAC,EAAE,CAAC,IAAA,aAAG,EAAC,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAA;oBACvC,CAAC;gBACH,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AAED,kBAAe,GAAG,CAAA"}