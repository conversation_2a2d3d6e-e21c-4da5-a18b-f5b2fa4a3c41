<script lang="ts">
import mpx, { createApp } from '@mpxjs/core'
import apiProxy from '@mpxjs/api-proxy'
import { createPinia } from '@mpxjs/pinia'
import { useUserStore } from './stores/user'

// 使用API代理
mpx.use(apiProxy, { usePromise: true })

// 创建Pinia实例
const pinia = createPinia()
mpx.use(pinia)

createApp({
  async onLaunch() {
    // 初始化用户状态
    const userStore = useUserStore()
    await userStore.initUserState()
  },

  onShow() {
    // 应用显示时检查登录状态
    const userStore = useUserStore()
    userStore.checkLoginStatus()
  }
})
</script>

<style lang="stylus">
@import './styles/global.styl'

// 全局页面样式
page
  background-color #f5f5f5
  font-family -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif
  line-height 1.4
  color #333

// 全局滚动条样式
::-webkit-scrollbar
  width 0
  background transparent
</style>

<script type="application/json">
  {
    "pages": [
      "./pages/index/index",
      "./pages/login/index",
      "./pages/character/list",
      "./pages/character/detail",
      "./pages/practice/stroke",
      "./pages/progress/index",
      "./pages/profile/index"
    ],
    "tabBar": {
      "color": "#666666",
      "selectedColor": "#1AAD19",
      "backgroundColor": "#ffffff",
      "borderStyle": "black",
      "list": [
        {
          "pagePath": "pages/index/index",
          "text": "首页",
          "iconPath": "static/images/home.png",
          "selectedIconPath": "static/images/home-active.png"
        },
        {
          "pagePath": "pages/character/list",
          "text": "汉字",
          "iconPath": "static/images/character.png",
          "selectedIconPath": "static/images/character-active.png"
        },
        {
          "pagePath": "pages/progress/index",
          "text": "进度",
          "iconPath": "static/images/progress.png",
          "selectedIconPath": "static/images/progress-active.png"
        },
        {
          "pagePath": "pages/profile/index",
          "text": "我的",
          "iconPath": "static/images/profile.png",
          "selectedIconPath": "static/images/profile-active.png"
        }
      ]
    },
    "window": {
      "backgroundTextStyle": "light",
      "navigationBarBackgroundColor": "#1AAD19",
      "navigationBarTitleText": "汉字学习",
      "navigationBarTextStyle": "white"
    }
  }
</script>

<!--也可以通过以下形式用js输出json，便于书写注释和使用条件编译-->

<!--<script name="json">-->
<!--  // 可以写注释，通过defs注入的常量做一些判断之类的操作-->
<!--  module.exports = {-->
<!--    pages: [-->
<!--      './pages/index'-->
<!--    ]-->
<!--  }-->
<!--</script>-->
