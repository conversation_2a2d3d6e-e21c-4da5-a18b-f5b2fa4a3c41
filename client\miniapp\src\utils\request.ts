import mpx from '@mpxjs/core'
import { xfetch } from '@mpxjs/fetch'

// API基础配置
const API_BASE_URL = 'http://localhost:3000/api'

// Token存储键名
const ACCESS_TOKEN_KEY = 'access_token'
const REFRESH_TOKEN_KEY = 'refresh_token'
const USER_INFO_KEY = 'user_info'

// 响应接口定义
interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
}

// 用户信息接口
interface UserInfo {
  id: number
  name: string
  email?: string
  phone?: string
  avatar?: string
  wechatOpenid?: string
  wechatUnionid?: string
}

// Token信息接口
interface TokenInfo {
  accessToken: string
  refreshToken: string
  expiresIn: number
}

class RequestManager {
  private isRefreshing = false
  private failedQueue: Array<{
    resolve: (value: any) => void
    reject: (reason: any) => void
  }> = []

  constructor() {
    this.setupInterceptors()
  }

  /**
   * 设置请求拦截器
   */
  private setupInterceptors() {
    // 请求拦截器
    xfetch.interceptors.request.use((config) => {
      const token = this.getAccessToken()
      if (token) {
        config.headers = {
          ...config.headers,
          'Authorization': `Bearer ${token}`
        }
      }
      return config
    })

    // 响应拦截器
    xfetch.interceptors.response.use(
      (response) => {
        return response
      },
      async (error) => {
        const originalRequest = error.config

        // 如果是401错误且不是刷新token的请求
        if (error.response?.status === 401 && !originalRequest._retry) {
          if (this.isRefreshing) {
            // 如果正在刷新token，将请求加入队列
            return new Promise((resolve, reject) => {
              this.failedQueue.push({ resolve, reject })
            }).then(token => {
              originalRequest.headers['Authorization'] = `Bearer ${token}`
              return xfetch(originalRequest)
            }).catch(err => {
              return Promise.reject(err)
            })
          }

          originalRequest._retry = true
          this.isRefreshing = true

          try {
            const newToken = await this.refreshToken()
            this.processQueue(null, newToken)
            originalRequest.headers['Authorization'] = `Bearer ${newToken}`
            return xfetch(originalRequest)
          } catch (refreshError) {
            this.processQueue(refreshError, null)
            this.clearTokens()
            // 跳转到登录页面
            mpx.navigateTo({ url: '/pages/login/index' })
            return Promise.reject(refreshError)
          } finally {
            this.isRefreshing = false
          }
        }

        return Promise.reject(error)
      }
    )
  }

  /**
   * 处理等待队列
   */
  private processQueue(error: any, token: string | null) {
    this.failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error)
      } else {
        resolve(token)
      }
    })
    
    this.failedQueue = []
  }

  /**
   * 获取访问令牌
   */
  getAccessToken(): string | null {
    try {
      return mpx.getStorageSync(ACCESS_TOKEN_KEY)
    } catch (error) {
      console.error('获取访问令牌失败:', error)
      return null
    }
  }

  /**
   * 获取刷新令牌
   */
  getRefreshToken(): string | null {
    try {
      return mpx.getStorageSync(REFRESH_TOKEN_KEY)
    } catch (error) {
      console.error('获取刷新令牌失败:', error)
      return null
    }
  }

  /**
   * 保存令牌信息
   */
  saveTokens(tokenInfo: TokenInfo) {
    try {
      mpx.setStorageSync(ACCESS_TOKEN_KEY, tokenInfo.accessToken)
      mpx.setStorageSync(REFRESH_TOKEN_KEY, tokenInfo.refreshToken)
    } catch (error) {
      console.error('保存令牌失败:', error)
    }
  }

  /**
   * 清除令牌信息
   */
  clearTokens() {
    try {
      mpx.removeStorageSync(ACCESS_TOKEN_KEY)
      mpx.removeStorageSync(REFRESH_TOKEN_KEY)
      mpx.removeStorageSync(USER_INFO_KEY)
    } catch (error) {
      console.error('清除令牌失败:', error)
    }
  }

  /**
   * 保存用户信息
   */
  saveUserInfo(userInfo: UserInfo) {
    try {
      mpx.setStorageSync(USER_INFO_KEY, userInfo)
    } catch (error) {
      console.error('保存用户信息失败:', error)
    }
  }

  /**
   * 获取用户信息
   */
  getUserInfo(): UserInfo | null {
    try {
      return mpx.getStorageSync(USER_INFO_KEY)
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return null
    }
  }

  /**
   * 刷新访问令牌
   */
  private async refreshToken(): Promise<string> {
    const refreshToken = this.getRefreshToken()
    if (!refreshToken) {
      throw new Error('没有刷新令牌')
    }

    const response = await xfetch({
      url: `${API_BASE_URL}/user/refresh-token`,
      method: 'POST',
      data: { refreshToken }
    })

    if (response.data.success) {
      const tokenInfo = response.data.data
      this.saveTokens(tokenInfo)
      return tokenInfo.accessToken
    } else {
      throw new Error(response.data.message || '刷新令牌失败')
    }
  }

  /**
   * 检查是否已登录
   */
  isLoggedIn(): boolean {
    const token = this.getAccessToken()
    const userInfo = this.getUserInfo()
    return !!(token && userInfo)
  }

  /**
   * 通用请求方法
   */
  async request<T = any>(config: {
    url: string
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
    data?: any
    params?: any
  }): Promise<ApiResponse<T>> {
    try {
      const response = await xfetch({
        url: `${API_BASE_URL}${config.url}`,
        method: config.method || 'GET',
        data: config.data,
        params: config.params
      })

      return response.data
    } catch (error: any) {
      console.error('请求失败:', error)
      return {
        success: false,
        message: error.message || '请求失败'
      }
    }
  }

  /**
   * GET请求
   */
  get<T = any>(url: string, params?: any): Promise<ApiResponse<T>> {
    return this.request<T>({ url, method: 'GET', params })
  }

  /**
   * POST请求
   */
  post<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({ url, method: 'POST', data })
  }

  /**
   * PUT请求
   */
  put<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({ url, method: 'PUT', data })
  }

  /**
   * DELETE请求
   */
  delete<T = any>(url: string): Promise<ApiResponse<T>> {
    return this.request<T>({ url, method: 'DELETE' })
  }
}

// 创建请求管理器实例
const requestManager = new RequestManager()

export default requestManager
export type { ApiResponse, UserInfo, TokenInfo }
